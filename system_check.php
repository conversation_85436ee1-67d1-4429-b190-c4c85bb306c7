<?php
// فحص شامل للنظام
require_once 'config/database.php';

$db = getDatabase();

echo "<h1>فحص شامل لنظام WIDDX OMS</h1>";

$errors = [];
$warnings = [];
$success = [];

// فحص الملفات الأساسية
$required_files = [
    'config/database.php' => 'ملف إعدادات قاعدة البيانات',
    'index.php' => 'الصفحة الرئيسية',
    'add_customer.php' => 'صفحة إضافة عميل',
    'add_order.php' => 'صفحة إضافة طلبية',
    'view_orders.php' => 'صفحة عرض الطلبيات',
    'view_order.php' => 'صفحة عرض تفاصيل الطلبية',
    'manage_customers.php' => 'صفحة إدارة العملاء',
    'get_customer_orders.php' => 'ملف جلب طلبيات العميل',
    'update_order_status.php' => 'ملف تحديث حالة الطلبية'
];

echo "<h2>فحص الملفات:</h2>";
foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "✅ $description ($file)<br>";
        $success[] = "ملف $description موجود";
    } else {
        echo "❌ $description ($file) - مفقود<br>";
        $errors[] = "ملف $description مفقود";
    }
}

// فحص المجلدات
$required_dirs = [
    'uploads' => 'مجلد الصور',
    'config' => 'مجلد الإعدادات'
];

echo "<h2>فحص المجلدات:</h2>";
foreach ($required_dirs as $dir => $description) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "✅ $description ($dir) - موجود وقابل للكتابة<br>";
            $success[] = "مجلد $description جاهز";
        } else {
            echo "⚠️ $description ($dir) - موجود لكن غير قابل للكتابة<br>";
            $warnings[] = "مجلد $description غير قابل للكتابة";
        }
    } else {
        echo "❌ $description ($dir) - مفقود<br>";
        $errors[] = "مجلد $description مفقود";
    }
}

// فحص قاعدة البيانات
echo "<h2>فحص قاعدة البيانات:</h2>";
try {
    $db = new Database();
    echo "✅ الاتصال بقاعدة البيانات ناجح<br>";
    $success[] = "الاتصال بقاعدة البيانات ناجح";
    
    // فحص الجداول
    $required_tables = [
        'customers' => 'جدول العملاء',
        'orders' => 'جدول الطلبيات',
        'order_items' => 'جدول عناصر الطلبيات'
    ];
    
    foreach ($required_tables as $table => $description) {
        try {
            $db->query("SELECT 1 FROM $table LIMIT 1");
            $db->execute();
            echo "✅ $description ($table)<br>";
            $success[] = "$description موجود";
        } catch (Exception $e) {
            echo "❌ $description ($table) - مفقود أو تالف<br>";
            $errors[] = "$description مفقود أو تالف";
        }
    }
    
    // فحص البيانات التجريبية
    echo "<h3>البيانات الموجودة:</h3>";
    
    $db->query("SELECT COUNT(*) as count FROM customers");
    $customers_count = $db->single()['count'];
    echo "عدد العملاء: $customers_count<br>";
    
    $db->query("SELECT COUNT(*) as count FROM orders");
    $orders_count = $db->single()['count'];
    echo "عدد الطلبيات: $orders_count<br>";
    
    $db->query("SELECT COUNT(*) as count FROM order_items");
    $items_count = $db->single()['count'];
    echo "عدد عناصر الطلبيات: $items_count<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "<br>";
    $errors[] = "خطأ في الاتصال بقاعدة البيانات";
}

// فحص إعدادات PHP
echo "<h2>فحص إعدادات PHP:</h2>";

$php_settings = [
    'file_uploads' => 'رفع الملفات',
    'session.auto_start' => 'بدء الجلسات التلقائي'
];

foreach ($php_settings as $setting => $description) {
    $value = ini_get($setting);
    if ($setting == 'file_uploads') {
        if ($value) {
            echo "✅ $description مفعل<br>";
            $success[] = "$description مفعل";
        } else {
            echo "❌ $description معطل<br>";
            $errors[] = "$description معطل";
        }
    }
}

// عرض الملخص
echo "<h2>ملخص الفحص:</h2>";

if (count($errors) > 0) {
    echo "<div style='color: red; background: #ffe6e6; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ أخطاء يجب إصلاحها:</h3>";
    foreach ($errors as $error) {
        echo "• $error<br>";
    }
    echo "</div>";
}

if (count($warnings) > 0) {
    echo "<div style='color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>⚠️ تحذيرات:</h3>";
    foreach ($warnings as $warning) {
        echo "• $warning<br>";
    }
    echo "</div>";
}

if (count($success) > 0) {
    echo "<div style='color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>✅ العناصر السليمة:</h3>";
    foreach ($success as $item) {
        echo "• $item<br>";
    }
    echo "</div>";
}

// النتيجة النهائية
if (count($errors) == 0) {
    echo "<div style='color: green; background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
    echo "<h2>🎉 النظام جاهز للاستخدام!</h2>";
    echo "<p>جميع المكونات تعمل بشكل صحيح</p>";
    echo "<a href='index.php' style='display: inline-block; padding: 10px 20px; background-color: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 10px;'>الانتقال للنظام</a>";
    echo "</div>";
} else {
    echo "<div style='color: red; background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
    echo "<h2>⚠️ يوجد مشاكل تحتاج إصلاح</h2>";
    echo "<p>يرجى إصلاح الأخطاء المذكورة أعلاه</p>";
    echo "</div>";
}

echo "<br><a href='install.php' style='display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px;'>إعادة تثبيت قاعدة البيانات</a>";
echo "<a href='update_database.php' style='display: inline-block; padding: 10px 20px; background-color: #6c757d; color: white; text-decoration: none; border-radius: 5px; margin: 10px;'>تحديث قاعدة البيانات</a>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    direction: rtl;
    line-height: 1.6;
}
h1, h2, h3 {
    color: #333;
}
</style>
