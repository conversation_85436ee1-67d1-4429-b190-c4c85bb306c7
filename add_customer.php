<?php
/**
 * صفحة إضافة عميل جديد - محدثة لاستخدام النظام الجديد OOP
 */

// تحميل النظام
require_once 'config/database_fixed.php';

// إعداد متغيرات الصفحة
$pageTitle = 'إضافة عميل جديد';
$currentPage = 'add_customer';

$message = '';

// معالجة إضافة عميل جديد
if ($_POST && isset($_POST['action']) && $_POST['action'] == 'add_customer') {
    try {
        if (class_exists('Customer')) {
            $customerManager = new Customer();
            $customerData = [
                'name' => $_POST['name'] ?? '',
                'phone' => $_POST['phone'] ?? '',
                'email' => $_POST['email'] ?? '',
                'address' => $_POST['address'] ?? ''
            ];

            $result = $customerManager->create($customerData);

            if ($result['success']) {
                $customer_id = $result['customer_id'];
                $message = '<div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> ' . $result['message'] . '
                    <div class="mt-2">
                        <a href="add_order.php?customer_id=' . $customer_id . '" class="btn btn-sm btn-success">
                            <i class="fas fa-plus"></i> إضافة طلبية لهذا العميل
                        </a>
                        <a href="add_customer.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-user-plus"></i> إضافة عميل آخر
                        </a>
                    </div>
                </div>';
            } else {
                $message = '<div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> ' . $result['message'] . '
                </div>';
            }
        } else {
            // استخدام الطريقة القديمة
            $db = getDatabase();
            $name = trim($_POST['name']);
            $phone = trim($_POST['phone']);
            $email = trim($_POST['email']);
            $address = trim($_POST['address']);

            if (!empty($name)) {
                $db->query("INSERT INTO customers (name, phone, email, address) VALUES (:name, :phone, :email, :address)");
                $db->bind(':name', $name);
                $db->bind(':phone', $phone);
                $db->bind(':email', $email);
                $db->bind(':address', $address);

                if ($db->execute()) {
                    $customer_id = $db->lastInsertId();
                    $message = '<div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> تم إضافة العميل بنجاح!
                        <div class="mt-2">
                            <a href="add_order.php?customer_id=' . $customer_id . '" class="btn btn-sm btn-success">
                                <i class="fas fa-plus"></i> إضافة طلبية لهذا العميل
                            </a>
                            <a href="add_customer.php" class="btn btn-sm btn-primary">
                                <i class="fas fa-user-plus"></i> إضافة عميل آخر
                            </a>
                        </div>
                    </div>';
                } else {
                    $message = '<div class="alert alert-danger">حدث خطأ في إضافة العميل!</div>';
                }
            } else {
                $message = '<div class="alert alert-warning">يرجى إدخال اسم العميل!</div>';
            }
        }
    } catch (Exception $e) {
        $message = '<div class="alert alert-danger">حدث خطأ: ' . $e->getMessage() . '</div>';
    }
}

// تضمين الـ header
if (file_exists('includes/header.php')) {
    include 'includes/header.php';
} else {
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?php echo $pageTitle; ?> - WIDDX OMS</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <?php if (file_exists('assets/css/main.css')): ?>
            <link href="assets/css/main.css" rel="stylesheet">
        <?php endif; ?>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container">
                <a class="navbar-brand" href="index.php">
                    <i class="fas fa-box"></i> WIDDX OMS
                </a>
                <div class="navbar-nav">
                    <a class="nav-link" href="index.php">الرئيسية</a>
                    <a class="nav-link active" href="add_customer.php">إضافة عميل</a>
                    <a class="nav-link" href="add_order.php">إضافة طلبية</a>
                    <a class="nav-link" href="view_orders.php">عرض الطلبيات</a>
                    <a class="nav-link" href="manage_customers.php">إدارة العملاء</a>
                </div>
            </div>
        </nav>
    <?php
}
?>

<div class="container mt-4">
        <?php echo $message; ?>
        
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header text-center">
                        <h4><i class="fas fa-user-plus"></i> إضافة عميل جديد</h4>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="add_customer">
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user"></i> اسم العميل *
                                </label>
                                <input type="text" class="form-control" id="name" name="name" required
                                       placeholder="أدخل اسم العميل الكامل"
                                       value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">
                                    <i class="fas fa-phone"></i> رقم الهاتف
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       placeholder="مثال: 01234567890">
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope"></i> البريد الإلكتروني
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       placeholder="مثال: <EMAIL>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">
                                    <i class="fas fa-map-marker-alt"></i> العنوان
                                </label>
                                <textarea class="form-control" id="address" name="address" rows="3" 
                                          placeholder="أدخل العنوان التفصيلي"></textarea>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save"></i> حفظ العميل
                                </button>
                                <a href="index.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-right"></i> العودة للرئيسية
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- نصائح مفيدة -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-lightbulb text-warning"></i> نصائح مفيدة</h6>
                        <ul class="mb-0">
                            <li>اسم العميل مطلوب، باقي البيانات اختيارية</li>
                            <li>يمكنك إضافة طلبية مباشرة بعد حفظ العميل</li>
                            <li>تأكد من صحة رقم الهاتف للتواصل</li>
                            <li>العنوان مهم لتسليم الطلبيات</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php
// تضمين الـ footer
if (file_exists('includes/footer.php')) {
    include 'includes/footer.php';
} else {
    ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <?php if (file_exists('assets/js/main.js')): ?>
        <script src="assets/js/main.js"></script>
    <?php endif; ?>
    <script>
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // تركيز على حقل الاسم
            document.getElementById('name').focus();

            // تحسين إرسال النموذج
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
                    }
                });
            }
        });
    </script>
    </body>
    </html>
    <?php
}
?>
