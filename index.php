<?php
/**
 * الصفحة الرئيسية لنظام WIDDX OMS
 * تم تحديثها لاستخدام النظام الجديد OOP
 */

// تحميل النظام
require_once 'config/database_fixed.php';

// إعداد متغيرات الصفحة
$pageTitle = 'الرئيسية';
$currentPage = 'index';

try {
    // إنشاء مثيلات من الكلاسات
    if (class_exists('Customer')) {
        $customerManager = new Customer();
        $customerStats = $customerManager->getStats();
        $activeCustomers = $customerManager->getActiveCustomers();
    } else {
        // استخدام الطريقة القديمة كبديل
        $db = getDatabase();
        $customerStats = [
            'total_customers' => 0,
            'active_customers' => 0,
            'new_this_month' => 0
        ];
        $activeCustomers = [];

        // جلب إحصائيات العملاء
        $db->query("SELECT COUNT(*) as count FROM customers");
        $customerStats['total_customers'] = $db->single()['count'] ?? 0;
    }

    if (class_exists('Order')) {
        $orderManager = new Order();
        $orderStats = $orderManager->getStats();
    } else {
        // استخدام الطريقة القديمة كبديل
        $db = getDatabase();
        $orderStats = [
            'total_orders' => 0,
            'pending_orders' => 0,
            'processing_orders' => 0,
            'completed_orders' => 0
        ];

        // جلب إحصائيات الطلبيات
        $db->query("SELECT COUNT(*) as count FROM orders");
        $orderStats['total_orders'] = $db->single()['count'] ?? 0;

        $db->query("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'");
        $orderStats['pending_orders'] = $db->single()['count'] ?? 0;

        $db->query("SELECT COUNT(*) as count FROM orders WHERE status = 'processing'");
        $orderStats['processing_orders'] = $db->single()['count'] ?? 0;

        $db->query("SELECT COUNT(*) as count FROM orders WHERE status = 'completed'");
        $orderStats['completed_orders'] = $db->single()['count'] ?? 0;
    }
} catch (Exception $e) {
    // في حالة حدوث خطأ، استخدم قيم افتراضية
    $customerStats = ['total_customers' => 0, 'active_customers' => 0, 'new_this_month' => 0];
    $orderStats = ['total_orders' => 0, 'pending_orders' => 0, 'processing_orders' => 0, 'completed_orders' => 0];
    $activeCustomers = [];
}

// تضمين الـ header
if (file_exists('includes/header.php')) {
    include 'includes/header.php';
} else {
    // Header بديل
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?php echo $pageTitle; ?> - WIDDX OMS</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <?php if (file_exists('assets/css/main.css')): ?>
            <link href="assets/css/main.css" rel="stylesheet">
        <?php endif; ?>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container">
                <a class="navbar-brand" href="index.php">
                    <i class="fas fa-box"></i> WIDDX OMS
                </a>
                <div class="navbar-nav">
                    <a class="nav-link active" href="index.php">الرئيسية</a>
                    <a class="nav-link" href="add_customer.php">إضافة عميل</a>
                    <a class="nav-link" href="add_order.php">إضافة طلبية</a>
                    <a class="nav-link" href="view_orders.php">عرض الطلبيات</a>
                    <a class="nav-link" href="manage_customers.php">إدارة العملاء</a>
                </div>
            </div>
        </nav>
    <?php
}
?>
    <!-- المحتوى الرئيسي -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-header text-center">
                        <h2><i class="fas fa-home"></i> مرحباً بك في نظام إدارة الطلبيات</h2>
                    </div>
                    <div class="card-body text-center">
                        <p class="lead">نظام شامل لإدارة العملاء والمنتجات والطلبيات</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">

            <div class="col-md-2 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h3><?php echo $customerStats['total_customers'] ?? 0; ?></h3>
                        <p>العملاء</p>
                    </div>
                </div>
            </div>

            <div class="col-md-2 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                        <h3><?php echo $orderStats['total_orders'] ?? 0; ?></h3>
                        <p>إجمالي الطلبيات</p>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-warning">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h3><?php echo $orderStats['pending_orders'] ?? 0; ?></h3>
                        <p>طلبيات معلقة</p>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-info">
                    <div class="card-body text-center">
                        <i class="fas fa-cogs fa-2x mb-2"></i>
                        <h3><?php echo $orderStats['processing_orders'] ?? 0; ?></h3>
                        <p>قيد التنفيذ</p>
                    </div>
                </div>
            </div>

            <div class="col-md-2 mb-3">
                <div class="card stats-card bg-success">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h3><?php echo $orderStats['completed_orders'] ?? 0; ?></h3>
                        <p>مكتملة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-user-plus feature-icon"></i>
                        <h5 class="card-title">إضافة عميل جديد</h5>
                        <a href="add_customer.php" class="btn btn-primary">إضافة عميل</a>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-plus-circle feature-icon"></i>
                        <h5 class="card-title">إضافة طلبية جديدة</h5>
                        <a href="add_order.php" class="btn btn-primary">إضافة طلبية</a>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-list feature-icon"></i>
                        <h5 class="card-title">عرض جميع الطلبيات</h5>
                        <a href="view_orders.php" class="btn btn-primary">عرض الطلبيات</a>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-users-cog feature-icon"></i>
                        <h5 class="card-title">إدارة العملاء</h5>
                        <a href="manage_customers.php" class="btn btn-primary">إدارة العملاء</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- لوحة تحكم العملاء والطلبيات -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tachometer-alt"></i> لوحة تحكم العملاء والطلبيات</h5>
            </div>
            <div class="card-body">

                <?php if (count($activeCustomers) > 0): ?>
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-info-circle"></i>
                        <strong>يتم عرض العملاء الذين لديهم طلبيات معلقة أو قيد التنفيذ فقط</strong>
                    </div>
                    <div class="accordion" id="customersAccordion">
                        <?php foreach ($activeCustomers as $index => $customer): ?>
                            <div class="accordion-item mb-2">
                                <h2 class="accordion-header" id="heading<?php echo $customer['id']; ?>">
                                    <button class="accordion-button collapsed customer-button" type="button"
                                            data-bs-toggle="collapse"
                                            data-bs-target="#collapse<?php echo $customer['id']; ?>"
                                            aria-expanded="false"
                                            aria-controls="collapse<?php echo $customer['id']; ?>"
                                            onclick="loadCustomerOrders(<?php echo $customer['id']; ?>)">
                                        <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                            <div>
                                                <i class="fas fa-user me-2"></i>
                                                <strong><?php echo htmlspecialchars($customer['name']); ?></strong>
                                                <?php if (!empty($customer['phone'])): ?>
                                                    <small class="text-muted ms-2">
                                                        <i class="fas fa-phone"></i> <?php echo htmlspecialchars($customer['phone']); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                            <div class="customer-stats">
                                                <?php
                                                $active_orders = $customer['pending_count'] + $customer['processing_count'];
                                                ?>
                                                <span class="badge bg-primary me-1">
                                                    <i class="fas fa-exclamation-circle"></i> <?php echo $active_orders; ?> طلبية نشطة
                                                </span>
                                                <?php if ($customer['pending_count'] > 0): ?>
                                                    <span class="badge bg-warning text-dark me-1">
                                                        <i class="fas fa-clock"></i> معلقة: <?php echo $customer['pending_count']; ?>
                                                    </span>
                                                <?php endif; ?>
                                                <?php if ($customer['processing_count'] > 0): ?>
                                                    <span class="badge bg-info me-1">
                                                        <i class="fas fa-cogs"></i> قيد التنفيذ: <?php echo $customer['processing_count']; ?>
                                                    </span>
                                                <?php endif; ?>
                                                <span class="badge bg-secondary me-1">
                                                    <i class="fas fa-list"></i> إجمالي: <?php echo $customer['orders_count']; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </button>
                                </h2>
                                <div id="collapse<?php echo $customer['id']; ?>"
                                     class="accordion-collapse collapse"
                                     aria-labelledby="heading<?php echo $customer['id']; ?>"
                                     data-bs-parent="#customersAccordion">
                                    <div class="accordion-body">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6><i class="fas fa-list"></i> طلبيات العميل</h6>
                                            <a href="add_order.php?customer_id=<?php echo $customer['id']; ?>"
                                               class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> إضافة طلبية جديدة
                                            </a>
                                        </div>

                                        <div id="orders-container-<?php echo $customer['id']; ?>">
                                            <div class="text-center py-3">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">جاري التحميل...</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5 class="text-success">ممتاز! لا توجد طلبيات معلقة أو قيد التنفيذ</h5>
                        <p class="text-muted">جميع الطلبيات مكتملة أو لا توجد طلبيات نشطة حالياً</p>
                        <div class="mt-3">
                            <a href="add_order.php" class="btn btn-primary me-2">
                                <i class="fas fa-plus-circle"></i> إضافة طلبية جديدة
                            </a>
                            <a href="view_orders.php" class="btn btn-outline-secondary">
                                <i class="fas fa-list"></i> عرض جميع الطلبيات
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

<?php
// تضمين الـ footer
if (file_exists('includes/footer.php')) {
    include 'includes/footer.php';
} else {
    // Footer بديل
    ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <?php if (file_exists('assets/js/main.js')): ?>
        <script src="assets/js/main.js"></script>
    <?php endif; ?>
    <script>
        // وظائف أساسية
        function loadCustomerOrders(customerId) {
            if (typeof widdxSystem !== 'undefined') {
                widdxSystem.loadCustomerOrders(customerId);
            } else {
                // طريقة بديلة
                const container = document.getElementById(`orders-container-${customerId}`);
                if (container) {
                    fetch(`get_customer_orders.php?customer_id=${customerId}`)
                        .then(response => response.text())
                        .then(data => container.innerHTML = data)
                        .catch(error => console.error('Error:', error));
                }
            }
        }

        function updateOrderStatus(orderId, status) {
            if (typeof widdxSystem !== 'undefined') {
                widdxSystem.updateOrderStatus(orderId, status);
            } else {
                // طريقة بديلة
                const formData = new FormData();
                formData.append('order_id', orderId);
                formData.append('status', status);
                formData.append('update_status', '1');

                fetch('update_order_status.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('حدث خطأ في تحديث الحالة');
                    }
                })
                .catch(error => alert('حدث خطأ في الاتصال'));
            }
        }
    </script>
    </body>
    </html>
    <?php
}
?>
