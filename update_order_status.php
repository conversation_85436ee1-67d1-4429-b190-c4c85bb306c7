<?php
header('Content-Type: application/json; charset=utf-8');
require_once 'config/database.php';

$db = getDatabase();
$response = ['success' => false, 'message' => ''];

// التحقق من البيانات المرسلة
if ($_POST && isset($_POST['order_id']) && isset($_POST['status']) && isset($_POST['update_status'])) {
    $order_id = intval($_POST['order_id']);
    $status = $_POST['status'];
    
    // التحقق من صحة الحالة
    $valid_statuses = ['pending', 'processing', 'completed', 'cancelled'];
    if (!in_array($status, $valid_statuses)) {
        $response['message'] = 'حالة غير صحيحة';
        echo json_encode($response);
        exit;
    }
    
    // تحديث حالة الطلبية
    $db->query("UPDATE orders SET status = :status, updated_at = CURRENT_TIMESTAMP WHERE id = :id");
    $db->bind(':status', $status);
    $db->bind(':id', $order_id);
    
    if ($db->execute()) {
        $response['success'] = true;
        $response['message'] = 'تم تحديث حالة الطلبية بنجاح';
        
        // جلب اسم العميل للرسالة
        $db->query("SELECT c.name FROM customers c JOIN orders o ON c.id = o.customer_id WHERE o.id = :order_id");
        $db->bind(':order_id', $order_id);
        $customer = $db->single();
        
        if ($customer) {
            $status_text = '';
            switch ($status) {
                case 'pending': $status_text = 'معلقة'; break;
                case 'processing': $status_text = 'قيد التنفيذ'; break;
                case 'completed': $status_text = 'مكتملة'; break;
                case 'cancelled': $status_text = 'ملغية'; break;
            }
            
            $response['message'] = "تم تحديث طلبية #{$order_id} للعميل {$customer['name']} إلى: {$status_text}";
        }
    } else {
        $response['message'] = 'حدث خطأ في تحديث الطلبية';
    }
} else {
    $response['message'] = 'بيانات غير مكتملة';
}

echo json_encode($response);
?>
